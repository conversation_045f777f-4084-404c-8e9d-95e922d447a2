<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.0.3">
  <diagram name="Page-1" id="49Z_-CBGwNvgt1gbwsUl">
    <mxGraphModel dx="2009" dy="1208" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="jIrd7PC0buNlh1nEQWz0-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9D5DFF;strokeColor=#3700CC;fontColor=#ffffff;" vertex="1" parent="1">
          <mxGeometry x="190" y="264" width="385" height="160" as="geometry" />
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="jIrd7PC0buNlh1nEQWz0-12" target="jIrd7PC0buNlh1nEQWz0-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-10" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;Presents proof of Authorization to Provider&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="jIrd7PC0buNlh1nEQWz0-6">
          <mxGeometry x="-0.05" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-5" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;Presents proof of Authorization to Gateway&lt;/font&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="jIrd7PC0buNlh1nEQWz0-3" target="jIrd7PC0buNlh1nEQWz0-11">
          <mxGeometry relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-3" value="Client App" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="365" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-4" value="Model Provider" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="365" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-11" value="Verifies access to requested model and provider based on defined policy" style="rounded=1;whiteSpace=wrap;html=1;gradientColor=none;fillOpacity=80;" vertex="1" parent="1">
          <mxGeometry x="303" y="280" width="245" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-12" value="Attaches securely stored credentials to call upstream provider to access model" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;fillColor=default;gradientColor=none;fillOpacity=80;" vertex="1" parent="1">
          <mxGeometry x="303" y="350" width="245" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jIrd7PC0buNlh1nEQWz0-13" value="&lt;b&gt;Envoy AI Gateway&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#FFFFFF;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="200" y="329.5" width="80" height="29" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
