processor:
  # RE2 regular expressions describing types that should be excluded from the generated documentation.
  ignoreTypes:
    - "(EnvoyProxy)List$"
  # RE2 regular expressions describing type fields that should be excluded from the generated documentation.
  ignoreFields:
    - "TypeMeta$"
  customMarkers:
    - name: "notImplementedHide"
      target: "field"

render:
  # Version of Kubernetes to use when generating links to Kubernetes API documentation.
  kubernetesVersion: 1.29
  knownTypes:
    - name: BackendObjectReference
      package: sigs.k8s.io/gateway-api/apis/v1
      link: https://gateway-api.sigs.k8s.io/references/spec/#gateway.networking.k8s.io/v1.BackendObjectReference
    - name: SecretObjectReference
      package: sigs.k8s.io/gateway-api/apis/v1
      link: https://gateway-api.sigs.k8s.io/references/spec/#gateway.networking.k8s.io/v1.SecretObjectReference
    - name: LocalPolicyTargetReferenceWithSectionName
      package: sigs.k8s.io/gateway-api/apis/v1alpha2
      link: https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io/v1alpha2.LocalPolicyTargetReferenceWithSectionName
    - name: LocalPolicyTargetReference
      package: sigs.k8s.io/gateway-api/apis/v1alpha2
      link: https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io/v1alpha2.LocalPolicyTargetReference
    - name: Duration
      package: sigs.k8s.io/gateway-api/apis/v1
      link: https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io/v1.Duration
    - name: PolicyStatus
      package: sigs.k8s.io/gateway-api/apis/v1alpha2
      link: https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io/v1alpha2.PolicyStatus
    - name: HTTPRouteTimeouts
      package: sigs.k8s.io/gateway-api/apis/v1
      link: https://gateway-api.sigs.k8s.io/reference/spec/?h=httproutetimeouts#httproutetimeouts
    - name: LocalObjectReference
      package: sigs.k8s.io/gateway-api/apis/v1
      link: https://gateway-api.sigs.k8s.io/reference/spec/?h=httproutetimeouts#localobjectreference
    - name: ExtProc
      package: github.com/envoyproxy/gateway/api/v1alpha1
      link: https://gateway.envoyproxy.io/docs/api/extension_types/#extproc
    - name: OIDC
      package: github.com/envoyproxy/gateway/api/v1alpha1
      link: https://gateway.envoyproxy.io/docs/api/extension_types/#oidc

navigation:
  includeTOC: true
  includeBackToTopLinks: true
