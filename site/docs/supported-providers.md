---
id: supported-providers
title: Supported AI Providers
sidebar_position: 8
---

Since the Envoy AI Gateway is designed to provide a Unified API for routing and managing LLM/AI traffic, it supports various AI providers out of the box.
A "support of provider" means two things: the API schema support and the Authentication support. \
The former can be configured in the `AIBackend` resource's `schema` field, while the latter is configured in the `BackendSecurityPolicy`.

Below is a table of currently supported providers and their respective configurations.

| Provider Name                                                                                         |      API Schema Config on [AIBackend]       | Upstream Authentication Config on [BackendSecurityPolicy] | Status | Note                                                                                                                                                   |
|-------------------------------------------------------------------------------------------------------|:--------------------------------------------------:|:---------------------------------------------------------:|:------:|--------------------------------------------------------------------------------------------------------------------------------------------------------|
| [OpenAI](https://platform.openai.com/docs/api-reference)                                              |         `{"name":"OpenAI","version":"v1"}`         |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [AWS Bedrock](https://docs.aws.amazon.com/bedrock/latest/APIReference/)                               |              `{"name":"AWSBedrock"}`               |                 [AWS Bedrock Credentials]                 |   ✅    |                                                                                                                                                        |
| [Azure OpenAI](https://learn.microsoft.com/en-us/azure/ai-services/openai/reference)                  | `{"name":"AzureOpenAI","version":"2025-01-01-preview"}` |                    [Azure Credentials]                    |   ✅    |                                                                                                                                                        |
| [Google Gemini on AI Studio](https://ai.google.dev/gemini-api/docs/openai)                            |   `{"name":"OpenAI","version":"v1beta/openai"}`    |                         [API Key]                         |   ✅    | Only the OpenAI compatible endpoint                                                                                                                    |
| [Groq](https://console.groq.com/docs/openai)                                                          |     `{"name":"OpenAI","version":"openai/v1"}`      |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [Grok](https://docs.x.ai/docs/api-reference?utm_source=chatgpt.com#chat-completions)                  |         `{"name":"OpenAI","version":"v1"}`         |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [Together AI](https://docs.together.ai/docs/openai-api-compatibility)                                 |         `{"name":"OpenAI","version":"v1"}`         |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [Cohere](https://docs.cohere.com/v2/docs/compatibility-api)                                           |  `{"name":"OpenAI","version":"compatibility/v1"}`  |                         [API Key]                         |   ✅    | Only the OpenAI compatible endpoint                                                                                                                    |
| [Mistral](https://docs.mistral.ai/api/#tag/chat/operation/chat_completion_v1_chat_completions_post)   |         `{"name":"OpenAI","version":"v1"}`         |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [DeepInfra](https://deepinfra.com/docs/inference)                                                     |     `{"name":"OpenAI","version":"v1/openai"}`      |                         [API Key]                         |   ✅    | Only the OpenAI compatible endpoint                                                                                                                    |
| [DeepSeek](https://api-docs.deepseek.com/)                                                            |         `{"name":"OpenAI","version":"v1"}`         |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [Hunyuan](https://cloud.tencent.com/document/product/1729/111007)                                     |         `{"name":"OpenAI","version":"v1"}`         |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [Tencent LLM Knowledge Engine](https://www.tencentcloud.com/document/product/1255/70381?lang=en)      |         `{"name":"OpenAI","version":"v1"}`         |                         [API Key]                         |   ✅    |                                                                                                                                                        |
| [Google Vertex AI](https://cloud.google.com/vertex-ai/docs/reference/rest)                            |                        N/A                         |                            N/A                            |   🚧   | Work-in-progress: [issue#609]                                                                                                                          |
| [Anthropic on Vertex AI](https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/claude) |                        N/A                         |                            N/A                            |   🚧   | Work-in-progress: [issue#609]                                                                                                                          |
| Self-hosted-models                                                                                    |         `{"name":"OpenAI","version":"v1"}`         |                            N/A                            |   ⚠️   | Depending on the API schema spoken by self-hosted servers. For example, [vLLM] speaks the OpenAI format. Also, API Key auth can be configured as well. |

[AIBackend]: api/api.mdx#AIBackendspec
[BackendSecurityPolicy]: api/api.mdx#backendsecuritypolicyspec
[API Key]: api/api.mdx#backendsecuritypolicyapikey
[AWS Bedrock Credentials]: api/api.mdx#backendsecuritypolicyawscredentials
[Azure Credentials]: api/api.mdx#backendsecuritypolicyazurecredentials
[issue#609]: https://github.com/envoyproxy/ai-gateway/issues/609
[vLLM]: https://docs.vllm.ai/en/v0.8.3/serving/openai_compatible_server.html
