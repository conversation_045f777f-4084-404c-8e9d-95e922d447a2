---
id: cli
title: "Envoy AI Gateway CLI"
sidebar_position: 4
---

# Envoy AI Gateway CLI (aigw)

The Envoy AI Gateway CLI, `aigw`, is a command-line interface that provides a set of useful tools for using the gateway.

:::warning
The CLI is experimental and currently under active development.
:::

Currently, you can do the following with the `aigw` CLI:

- **Run**: Run the Envoy AI Gateway locally as a standalone proxy with a given configuration file without any dependencies such as docker or Kubernetes.
- **Translate**: Translate a given Envoy AI Gateway configuration file to an Envoy Gateway configuration file.
