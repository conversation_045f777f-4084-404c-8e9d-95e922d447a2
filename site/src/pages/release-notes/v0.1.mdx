---
title: Envoy AI Gateway v0.1.x Release Series
description: The first official release series of Envoy AI Gateway
toc_min_heading_level: 2
toc_max_heading_level: 2
---

import Link from '@docusaurus/Link';
import {
  ReleaseSeriesLayout,
  ReleaseHeader,
  FeatureSectionCard,
  InfoBox,
  Dependencies,
  ItemList,
  PatchRelease
} from '../../components/ReleaseNotes';
import releaseData from '../../data/releases/v0.1.json';

export const { series, releases, navigation } = releaseData;
export const mainRelease = releases[0]; // v0.1.0
export const patchReleases = releases.slice(1).reverse(); // v0.1.5 - v0.1.1 (newest first)

<ReleaseSeriesLayout
  title={series.title}
  subtitle={series.subtitle}
  seriesVersion={series.version}
  badgeText={series.badge}
  badgeType={series.badgeType}
  nextSeries={navigation.next}
>

# 🎉 The First Official Release!

<ReleaseHeader
  version={mainRelease.version}
  date={mainRelease.date}
  type={mainRelease.type}
  tags={mainRelease.tags}
>

{mainRelease.overview}

This foundational release establishes Envoy AI Gateway as a production-ready solution for managing AI services in Kubernetes environments, providing the essential building blocks for enterprise AI infrastructure.

</ReleaseHeader>

<InfoBox type="info" title="Installation">
  For installation instructions, see our <Link to="/docs/getting-started/installation">Getting Started Guide</Link>.
</InfoBox>

## ✨ Core Features

{mainRelease.features.map((featureSection, index) => (
  <FeatureSectionCard key={index} section={featureSection} />
))}

## API Design

The v0.1.x series establishes the core API design for Envoy AI Gateway:

<ItemList items={mainRelease.apiChanges} />

## 📦 Dependencies Versions

<Dependencies title="Dependencies and Infrastructure" dependencies={mainRelease.dependencies} />


## ⏩ Patch Releases

{patchReleases.map((release, index) => (
  <PatchRelease
    key={index}
    version={release.version}
    date={release.date}
    type={release.type}
    tags={release.tags}
    overview={release.overview}
    features={release.features}
    bugFixes={release.bugFixes}
  />
))}

---

## 🙏 Acknowledgements

The v0.1.0 release represents months of collaborative effort from our founding community. Special thanks to early contributors and adopters who helped shape the initial architecture and provided invaluable feedback during the alpha and beta phases.

This milestone release sets the foundation for Envoy AI Gateway's future growth and establishes our commitment to providing enterprise-grade AI service management capabilities.


## 🔮 What Came Next

The success of the v0.1.x series paved the way for v0.2.0, which introduced:

- **Azure OpenAI Integration**
- **Sidecar Architecture for Better Performance**
- **Cross-Backend Failover and Retry Logic**
- **Enhanced CLI Tools**

<InfoBox type="success" title="v0.1 is now complete">
  The v0.1.x release is now complete. For the latest features and improvements, check out <Link to="/release-notes/v0.2">v0.2</Link>.
</InfoBox>

</ReleaseSeriesLayout>
