/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */
@import url('https://fonts.googleapis.com/css?family=Nunito:400,700,800');

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #6c1899;
  --ifm-color-primary-dark: #50166f;
  --ifm-color-primary-darker: #3a124f;
  --ifm-color-primary-darkest: #29073b;
  --ifm-color-primary-light: #8a20c4;
  --ifm-color-primary-lighter: #a339dc;
  --ifm-color-primary-lightest: #c257fc;
  --ifm-navbar-link-hover-color: var(--ifm-color-primary-darker);
  --ifm-code-font-size: 95%;
  --ifm-code-font-family: "Nunito", "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  --ifm-heading-font-family: "Nunito", "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  --ifm-font-family-base: "Nunito", "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
  --ifm-navbar-background-color: #b923ff;
  --ifm-footer-background-color: #f8ebff;
  --ifm-navbar-link-color: #fff;
  --ifm-line-height-base: 1.5;
  --ifm-container-width: 1340px;
  --ifm-container-width-xl: 1520px;
  --ifm-h1-font-size: 2.5rem;
  --ifm-h2-font-size: 2rem;
  --ifm-h3-font-size: 1.7rem;
  --ifm-h4-font-size: 1.4rem;
  --ifm-h5-font-size: 1.2rem;
  --ifm-h6-font-size: 1rem;

  /* New custom variables */
  --custom-content-line-height: 1.7;
  --custom-paragraph-spacing: 1.5rem;
  --custom-text-color: #2c3e50;
  --custom-heading-color: #1a202c;
  --custom-link-color: #6c1899;
  --custom-link-hover-color: #8a20c4;
  --custom-code-background: #f8f9fa;
  /* Required field badge colors */
  --custom-required-badge-bg: #fef2f2;
  --custom-required-badge-border: #fecaca;
  --custom-required-badge-text: #dc2626;


}

/* Index Header */
.heroBanner {
  font-family: "Nunito", "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  padding: 2rem 0rem 10rem 0rem;
  text-align: center;
  overflow: hidden;
  background-color: #6c1899;
  background-image: linear-gradient(0deg, #6c1899, #b923ff);
  clip-path: polygon(0 0, 100% 0, 100% 80%, 0 100%);
  color: #fff;
  position: relative;


  .hero__subtitle{
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  .heroImage{
    max-height: 30vh;
  }
  .buttons{
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 3rem;
    .button{
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 20, 147, 0.1) 100%
      );
      backdrop-filter: blur(10px);
      color: #fff;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 25px;
      padding: 0.8rem 2rem;
      font-weight: 600;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
      box-shadow: 0 4px 15px rgba(108, 24, 153, 0.2);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          rgba(255, 119, 0, 0.1) 0%,
          rgba(255, 255, 255, 0.1) 100%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: inherit;
      }

      &:hover {
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0.15) 0%,
          rgba(255, 20, 147, 0.15) 100%
        );
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(108, 24, 153, 0.3);

        &::before {
          opacity: 1;
          border-radius: inherit;
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 10px rgba(108, 24, 153, 0.2);
      }
    }
  }
}

  @media screen and (max-width: 996px) {
  .heroBanner {
    padding: 2rem 0rem 10rem 0rem;
    align-items: start;
    clip-path: polygon(0 0, 100% 0, 100% 87%, 0 100%);

    .hero__subtitle{
      font-size: 1.2rem;
    }

    .buttons{
      flex-direction: column-reverse;

    }
  }

}
  @media screen and (max-width: 500px) {
    .heroBanner {
      padding: 2rem 0rem 5rem 0rem;
      align-items: start;
      clip-path: polygon(0 0, 100% 0, 100% 90%, 0 100%);
      .heroImage{
        max-height: 15vh;
      }
    }
  }

.navbar {
    box-shadow: none;
}

.navbar-sidebar .menu__link{
  color: #fff;
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Word wrapping rules */
.markdown {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: keep-all;
  hyphens: none;
  color: var(--custom-text-color);
  line-height: var(--custom-content-line-height);
}

.markdown p {
  margin-bottom: var(--custom-paragraph-spacing);
}

.markdown h1, .markdown h2, .markdown h3, .markdown h4, .markdown h5, .markdown h6 {
  color: var(--custom-heading-color);
  font-weight: 700;
  margin-bottom: 1em;
}

.markdown h3 {
  color: var(--ifm-color-primary-light);
}

/* Enhanced Links */
.markdown a {
  color: var(--custom-link-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown a:hover {
  color: var(--custom-link-hover-color);
  border-bottom-color: currentColor;
}

.markdown table {
  width: fit-content;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(60, 60, 67, 0.15);
}

/* API Field Component Styles */
.api-field {
  display: flex;
  gap: 2rem;
  padding: 1.25rem;
  margin: 1rem 0;
  border: 1px solid rgba(60, 60, 67, 0.15);
  flex-wrap: wrap;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), 0 0 1px rgba(0, 0, 0, 0.08);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.api-field:hover {
  border-color: rgba(60, 60, 67, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04), 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Enum specific styling */
.api-field[data-type="enum"] {
  background-color: var(--custom-code-background);
  padding: 0.75rem 1rem;
  margin: 0.5rem 0;
  border-radius: 6px;
  border: 1px solid rgba(60, 60, 67, 0.12);
}

.api-field[data-type="enum"] .api-field__name {
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.9rem;
  color: var(--ifm-color-primary);
  background-color: var(--custom-code-background);
}

.api-field[data-type="enum"] .api-field__description {
  font-size: 0.875rem;
  color: var(--ifm-color-emphasis-700);
  margin-top: 0.25rem;
}

.api-field__left {
  flex: 0 0 400px;
  min-width: 0;
  margin-right: 1rem;
  position: relative;
}

.api-field__right {
  flex: 1;
  min-width: 300px;
  position: relative;
}

.api-field__name {
  font-family: var(--ifm-font-family-monospace);
  font-size: 1rem;
  padding: 0.2rem 0.4rem;
  margin-right: 0.5rem;
  background: var(--ifm-color-gray-100);
  border-radius: 4px;
  display: inline-block;
  word-break: break-word;
  max-width: 100%;
  vertical-align: middle;
  font-weight: 700;
  color: var(--ifm-color-primary);
}

.api-field__type {
  margin-top: 0.5rem;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  color: var(--ifm-color-emphasis-700);
}

.api-field__type > div {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}
.api-field__type::before {
  content: "Type: ";
  color: var(--ifm-color-emphasis);
  font-weight: 700;
}
.api-field__type a {
  color: var(--ifm-color-primary);
  text-decoration: none;
  padding: 0.2rem 0.4rem;
  padding-right: 1.8rem;
  border-radius: 4px;
  background-color: var(--custom-code-background);
  border: 1px solid rgba(60, 60, 67, 0.16);
  transition: all 0.2s ease;
  position: relative;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.api-field__type a::after {
  content: "→";
  position: absolute;
  right: 0.4rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1em;
  transition: transform 0.2s ease;
}

.api-field__type a:hover {
  text-decoration: none;
  background-color: var(--ifm-color-primary);
  border-color: var(--ifm-color-primary-dark);
  color: white;
}

.api-field__type a:hover::after {
  transform: translate(2px, -50%);
}

.api-field__badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.api-field__badge--required {
  color: var(--custom-required-badge-text);
  background: var(--custom-required-badge-bg);
  border: 1px solid var(--custom-required-badge-border);
  font-weight: 700;
  box-shadow: 0 0 0 1px var(--custom-required-badge-border);
}

.api-field__badge--optional {
  color: var(--ifm-color-gray-600);
  background: var(--ifm-color-gray-100);
  border: 1px solid var(--ifm-color-gray-200);
}

.api-field__default {
  right: 1rem;
  top: 0.5rem;
  font-size: 0.875rem;
  color: var(--ifm-color-emphasis);
  background: var(--custom-code-background);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(60, 60, 67, 0.12);
  font-family: var(--ifm-font-family-monospace);
  margin-bottom: 0.5rem;
}

.api-field__default::before {
  content: "Default Value: ";
  color: var(--ifm-color-emphasis);
  font-weight: 700;
}

.api-field__description {
  line-height: var(--custom-content-line-height);
  font-size: 0.9rem;
  width: 100%;
  overflow-x: hidden;
}

.api-field__description code {
  font-size: 0.8rem;
  padding: 0.2rem 0.4rem;
  background: var(--ifm-code-background);
  border-radius: 4px;
  color: var(--ifm-code-color);
  font-family: var(--ifm-font-family-monospace);
}

.api-field__description pre {
  margin: 1rem 0;
  padding: 1rem;
  background-color: var(--ifm-code-background);
  border-radius: var(--ifm-code-border-radius);
  max-width: 100%;
  overflow-x: auto;
}

.api-field__description pre code {
  background: transparent;
  padding: 0;
  font-size: var(--ifm-code-font-size);
  border-radius: 0;
  display: block;
  line-height: 1.5;
  color: inherit;
}

.api-field__description .prism-code {
  background-color: rgb(246, 247, 248);
  margin: 0;
  padding: 1rem;
  border-radius: var(--ifm-code-border-radius);
  font-family: var(--ifm-font-family-monospace);
  width: 100%;
  overflow-x: auto;
}

.api-field__description .codeBlock {
  display: block;
  white-space: pre;
  overflow-x: auto;
  padding: 0;
  margin: 0;
  color: rgb(57, 58, 52);
  width: 100%;
}

.api-field__description .language-yaml .codeBlock {
  color: rgb(57, 58, 52);
}

.api-field__enum {
  margin-top: 1rem;
  font-size: 0.875rem;
}

.api-field__enum code {
  margin-right: 0.5rem;
  padding: 0.2rem 0.4rem;
  background: var(--ifm-color-gray-100);
  border-radius: 4px;
}

@media screen and (max-width: 768px) {
  .api-field {
    flex-direction: column;
    gap: 1rem;
  }

  .api-field__left {
    flex: 0 0 auto;
    margin-right: 0;
    width: 100%;
  }

  .api-field__right {
    width: 100%;
  }

  .api-field__name {
    margin-bottom: 0.5rem;
  }
}

/* Override default table styles */
.theme-admonition-api-field td:first-child {
  width: 200px;
}

.theme-admonition-api-field td {
  border: none;
}

.theme-admonition-api-field tr {
  border-top: none;
}

.theme-admonition-api-field tr:last-child {
  border-bottom: none;
}
