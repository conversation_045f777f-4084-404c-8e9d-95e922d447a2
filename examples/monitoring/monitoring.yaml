# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 3s
    scrape_configs:
      - job_name: 'envoy-gateway-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          # Only scrape Envoy Gateway pods.
          - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
            regex: "envoy"
            action: keep
          - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_managed_by]
            regex: "envoy-gateway"
            action: keep
          - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_component]
            regex: "proxy"
            action: keep
          # Add labels to the metrics.
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
          # "metrics" is the port name for Envoy Proxy's prometheus metrics.
          # https://github.com/envoyproxy/gateway/blob/eb7266c042f4573d7e67c9ea02a19e0deff6e17f/internal/infrastructure/kubernetes/proxy/resource.go#L92
          # "aigw-metrics" is the port name for AI Gateway filter's prometheus metrics.
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            regex: "metrics|aigw-metrics"
            action: keep
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            regex: metrics
            # https://github.com/envoyproxy/gateway/blob/eb7266c042f4573d7e67c9ea02a19e0deff6e17f/internal/infrastructure/kubernetes/proxy/resource_provider.go#L576-L578
            replacement: /stats/prometheus
            target_label: __metrics_path__
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            regex: aigw-metrics
            replacement: /metrics
            target_label: __metrics_path__
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  - ingresses
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: monitoring
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.47.0
        args:
        - "--config.file=/etc/prometheus/prometheus.yml"
        - "--log.level=debug"
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        ports:
        - containerPort: 9090
          name: web
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: monitoring
  labels:
    app: prometheus
spec:
  selector:
    app: prometheus
  ports:
  - protocol: TCP
    port: 9090
    targetPort: 9090
    name: web
