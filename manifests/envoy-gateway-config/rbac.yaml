# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: list-ai-gateway-controller
rules:
  - apiGroups:
      - "aigateway.envoyproxy.io"
    resources:
      - "aigatewayroutes"
      - "aiservicebackends"
      - "backendSecurityPolicies"
    verbs:
      - "get"
      - "list"
      - "watch"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: list-ai-gateway-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: list-ai-gateway-controller
subjects:
  - kind: ServiceAccount
    name: envoy-gateway
    namespace: envoy-gateway-system
---
