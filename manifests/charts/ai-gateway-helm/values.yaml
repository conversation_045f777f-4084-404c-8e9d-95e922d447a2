# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

# Default values for ai-gateway-helm.

extProc:
  image:
    repository: docker.io/envoyproxy/ai-gateway-extproc
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  imagePullPolicy: IfNotPresent
  # One of "info", "debug", "trace", "warn", "error", "fatal", "panic".
  logLevel: info

controller:
  logLevel: info
  nameOverride: ""
  fullnameOverride: "ai-gateway-controller"
  # This is the namespace where the Envoy Gateway controller is installed.
  # It is hence where the gateway pods are running and the AI Gateway controller will mutate the pods.
  envoyGatewayNamespace: "envoy-gateway-system"

  # -- Service Account --
  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # Annotations to add to the service account
    annotations: {}
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: ""

  # Enable leader election mechanism for protecting against split brain if multiple operator pods/replicas are started.
  leaderElection:
    enabled: true

  # -- Deployment configs --
  image:
    repository: docker.io/envoyproxy/ai-gateway-controller
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  imagePullPolicy: IfNotPresent
  replicaCount: 1
  imagePullSecrets: []
  podAnnotations: {}
  podSecurityContext: {}
  securityContext: {}
  # Example of a podEnv
  # AWS STS request when rotating OIDC credentials will be configured to use AI_GATEWAY_STS_PROXY_URL proxy if set.
  #  - key: AI_GATEWAY_STS_PROXY_URL
  #    value: some-proxy-placeholder
  # Azure authentication request will be configured to use AI_GATEWAY_AZURE_PROXY_URL proxy if set.
  #  - key: AI_GATEWAY_AZURE_PROXY_URL
  #    value: some-proxy-placeholder
  podEnv: {}
  # Example of volumes
  #  - mountPath: /placeholder/path
  #    name: volume-name
  #    subPath: placeholder-sub-path
  #    configmap:
  #      defaultMode: placeholder
  #      name: configmap-name
  volumes: []
  service:
    type: ClusterIP
    ports:
      - name: mutating-webhook
        protocol: TCP
        port: 9443
        appProtocol: http
        targetPort: 9443
      - name: grpc
        protocol: TCP
        port: 1063
        appProtocol: grpc
        targetPort: 1063
      - name: http-metrics
        protocol: TCP
        appProtocol: http
        port: 9090
        targetPort: 9090

  mutatingWebhook:
    # The secret that contains the CA certificate and the server certificate for the webhook server.
    # Defaults to the self-signed cert generated by the project. The namespace of the secret
    # must be the same as the namespace of the controller installation.
    #
    # The self-signed cert is embedded as part of the helm chart, so it is not recommended for production use.
    # You can use the configurations below to specify a custom secret that contains the CA certificate and the server certificate.
    #
    # When specifying a secret generated by cert-manager, other fields (tlsCertName, tlsKeyName and caBundleName)
    # do not need to be set modified since they match the default values by cert-manager:
    # https://cert-manager.io/docs/usage/certificate/
    tlsCertSecretName: self-signed-cert-for-mutating-webhook
    # The name of the CA certificate in the secret to serve the webhook.
    tlsCertName: tls.crt
    # The name of the server certificate in the secret to serve the webhook.
    tlsKeyName: tls.key
    # The name of the CA bundle in the secret to use for the webhook.
    caBundleName: ca.crt
    # When cert-manager is enabled, the self-signed cert is created and rotated by cert-manager.
    certManager:
      enable: false
      # The name of the issuer.
      issuerName: self-signed-issuer-for-mutating-webhook
      # The name of the certificate.
      certificateName: self-signed-cert-for-mutating-webhook

  resources: {}
  nodeSelector: {}
  tolerations: []
  affinity: {}

