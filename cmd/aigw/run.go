// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package main

import (
	"bytes"
	"context"
	_ "embed"
	"fmt"
	"io"
	"log/slog"
	"net"
	"os"
	"path/filepath"
	"strings"

	"github.com/envoyproxy/gateway/cmd/envoy-gateway/root"
	egextension "github.com/envoyproxy/gateway/proto/extension"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/yaml"

	"github.com/envoyproxy/ai-gateway/cmd/extproc/mainlib"
	"github.com/envoyproxy/ai-gateway/filterapi"
	"github.com/envoyproxy/ai-gateway/internal/controller"
	"github.com/envoyproxy/ai-gateway/internal/extensionserver"
)

// This is the default configuration for the AI Gateway when <path> parameter is not given.
//
//go:embed ai-gateway-default-resources.yaml
var aiGatewayDefaultResources string

// This is the template for the Envoy Gateway configuration where PLACEHOLDER_TMPDIR will be replaced with the temporary
// directory where the resources are written to.
//
//go:embed envoy-gateway-config.yaml
var envoyGatewayConfigTemplate string

const (
	substitutionEnvAnnotationPrefix  = "substitution.aigw.run/env/"
	substitutionFileAnnotationPrefix = "substitution.aigw.run/file/"
)

type runCmdContext struct {
	// isDebug true if the original `agw run` command is run with debug mode. Using this to
	// set the log level of the external process currently. TODO: maybe simply expose the external process log level
	// at the command line, but that's an implementation detail, so I would rather not right now.
	isDebug bool
	// envoyGatewayResourcesOut is the output file for the envoy gateway resources.
	envoyGatewayResourcesOut io.Writer
	// stderrLogger is the logger for stderr.
	stderrLogger *slog.Logger
	// tmpdir is the temporary directory for the resources.
	tmpdir string
	// udsPath is the path to the UDS socket used by the AI Gateway extproc.
	udsPath string
	// fakeClientSet is the fake client set for the k8s resources. The objects are written to this client set and updated
	// during the translation.
	fakeClientSet *fake.Clientset
}

// run starts the AI Gateway locally for a given configuration.
//
// This will create a temporary directory and a file:
//  1. ${os.TempDir}/envoy-gateway-config.yaml: This contains the configuration for the Envoy Gateway agent to run, derived from envoyGatewayConfig.
//  2. ${os.TempDir}/envoy-ai-gateway-resources: This will contain the EG resource generated by the translation and deployed by EG.
func run(ctx context.Context, c cmdRun, stdout, stderr io.Writer) error {
	if !c.Debug {
		stderr = io.Discard
	}
	stderrLogger := slog.New(slog.NewTextHandler(stderr, &slog.HandlerOptions{}))
	if c.ShowDefault {
		_, err := stdout.Write([]byte(aiGatewayDefaultResources))
		if err != nil {
			panic(fmt.Sprintf("BUG: failed to write default resources: %v", err))
		}
		return nil
	}

	// First, we need to create the self-signed certificates used for communication between the EG and Envoy.
	// Certificates will be placed at /tmp/envoy-gateway/certs, which is currently is not configurable:
	// https://github.com/envoyproxy/gateway/blob/779c0a6bbdf7dacbf25a730140a112f99c239f0e/internal/infrastructure/host/infra.go#L22-L23
	//
	// TODO: maybe make it skip if the certs are already there, but not sure if it's worth the complexity.
	certGenOut := &bytes.Buffer{}
	certGen := root.GetRootCommand()
	certGen.SetOut(certGenOut)
	certGen.SetErr(certGenOut)
	certGen.SetArgs([]string{"certgen", "--local"})
	if err := certGen.ExecuteContext(ctx); err != nil {
		return fmt.Errorf("failed to execute certgen: %w: %s", err, certGenOut.String())
	}

	tmpdir := os.TempDir()
	egConfigPath := filepath.Join(tmpdir, "envoy-gateway-config.yaml")      // 1. The path to the Envoy Gateway config.
	resourcesTmpdir := filepath.Join(tmpdir, "/envoy-ai-gateway-resources") // 2. The path to the resources.
	if err := recreateDir(resourcesTmpdir); err != nil {
		return err
	}

	// Write the Envoy Gateway config which points to the resourcesTmpdir to tell Envoy Gateway where to find the resources.
	stderrLogger.Info("Writing Envoy Gateway config", "path", egConfigPath)
	err := os.WriteFile(egConfigPath, []byte(strings.ReplaceAll(
		envoyGatewayConfigTemplate, "PLACEHOLDER_TMPDIR", resourcesTmpdir),
	), 0o600)
	if err != nil {
		return fmt.Errorf("failed to write file %s: %w", egConfigPath, err)
	}

	// Write the Envoy Gateway resources into a file under resourcesTmpdir.
	resourceYamlPath := filepath.Join(resourcesTmpdir, "config.yaml")
	stderrLogger.Info("Creating Envoy Gateway resource file", "path", resourceYamlPath)
	f, err := os.Create(resourceYamlPath)
	defer func() {
		_ = f.Close()
	}()
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", resourceYamlPath, err)
	}
	udsPath := filepath.Join(tmpdir, "uds.sock")
	_ = os.Remove(udsPath)
	// Do the translation of the given AI Gateway resources Yaml into Envoy Gateway resources and write them to the file.
	runCtx := &runCmdContext{envoyGatewayResourcesOut: f, stderrLogger: stderrLogger, udsPath: udsPath, tmpdir: tmpdir, isDebug: c.Debug}
	// Use the default configuration if the path is not given.
	aiGatewayResourcesYaml := aiGatewayDefaultResources
	if c.Path != "" {
		var yamlBytes []byte
		yamlBytes, err = os.ReadFile(c.Path)
		if err != nil {
			return fmt.Errorf("failed to read file %s: %w", c.Path, err)
		}
		aiGatewayResourcesYaml = string(yamlBytes)
	}
	fakeCleint, err := runCtx.writeEnvoyResourcesAndRunExtProc(ctx, aiGatewayResourcesYaml)
	if err != nil {
		return err
	}

	lis, err := net.Listen("tcp", "localhost:1061")
	if err != nil {
		return fmt.Errorf("failed to listen: %w", err)
	}
	s := grpc.NewServer()
	extSrv := extensionserver.New(fakeCleint, ctrl.Log, udsPath)
	egextension.RegisterEnvoyGatewayExtensionServer(s, extSrv)
	grpc_health_v1.RegisterHealthServer(s, extSrv)
	go func() {
		<-ctx.Done()
		s.GracefulStop()
	}()
	go func() {
		if err := s.Serve(lis); err != nil {
			stderrLogger.Error("Failed to run extension server", "error", err)
		}
	}()

	// At this point, we have two things prepared:
	//  1. The Envoy Gateway config in egConfigPath.
	//  2. The Envoy Gateway resources in resourceYamlPath pointed by the config at egConfigPath.
	//
	// Now running the `envoy-gateway` CLI alternative below by passing `--config-path` to `egConfigPath`.
	// Then the agent will read the resources from the file pointed inside the config and start the Envoy process.

	server := root.GetRootCommand()
	egOut := &bytes.Buffer{}
	server.SetOut(egOut)
	server.SetErr(egOut)
	server.SetArgs([]string{"server", "--config-path", egConfigPath})
	if err := server.ExecuteContext(ctx); err != nil {
		return fmt.Errorf("failed to execute server: %w", err)
	}
	stderrLogger.Info("Envoy Gateway output", "output", egOut.String())
	return nil
}

// recreateDir removes the directory at the given path and creates a new one.
func recreateDir(path string) error {
	err := os.RemoveAll(path)
	if err != nil {
		return fmt.Errorf("failed to remove directory %s: %w", path, err)
	}
	err = os.MkdirAll(path, 0o755)
	if err != nil {
		return fmt.Errorf("failed to create directory %s: %w", path, err)
	}
	return nil
}

// writeEnvoyResourcesAndRunExtProc reads all resources from the given string, writes them to the output file, and runs
// external processes for EnvoyExtensionPolicy resources.
func (runCtx *runCmdContext) writeEnvoyResourcesAndRunExtProc(ctx context.Context, original string) (client.Client, error) {
	aigwRoutes, aigwBackends, backendSecurityPolicies, gateways, secrets, err := collectObjects(original, runCtx.envoyGatewayResourcesOut, runCtx.stderrLogger)
	if err != nil {
		return nil, fmt.Errorf("error collecting: %w", err)
	}
	if len(gateways) > 1 {
		return nil, fmt.Errorf("multiple gateways are not supported: %s", gateways[0].Name)
	}
	for _, bsp := range backendSecurityPolicies {
		spec := bsp.Spec
		if spec.AWSCredentials != nil && spec.AWSCredentials.OIDCExchangeToken != nil {
			// TODO: We can make it work by generalizing the rotation logic.
			return nil, fmt.Errorf("OIDC exchange token is not supported: %s", bsp.Name)
		}
	}

	// Do the substitution for the secrets.
	for _, s := range secrets {
		if err = runCtx.rewriteSecretWithAnnotatedLocation(s); err != nil {
			return nil, fmt.Errorf("failed to rewrite secret %s: %w", s.Name, err)
		}
	}

	fakeClient, _fakeClientSet, httpRoutes, eps, httpRouteFilter, backends, _, err := translateCustomResourceObjects(ctx, aigwRoutes, aigwBackends, backendSecurityPolicies, gateways, secrets, runCtx.udsPath, runCtx.stderrLogger)
	if err != nil {
		return nil, fmt.Errorf("error translating: %w", err)
	}
	runCtx.fakeClientSet = _fakeClientSet

	for _, hrf := range httpRouteFilter.Items {
		runCtx.mustClearSetOwnerReferencesAndStatusAndWriteObj(&hrf.TypeMeta, &hrf)
	}
	for _, hr := range httpRoutes.Items {
		runCtx.mustClearSetOwnerReferencesAndStatusAndWriteObj(&hr.TypeMeta, &hr)
	}
	for _, b := range backends.Items {
		runCtx.mustClearSetOwnerReferencesAndStatusAndWriteObj(&b.TypeMeta, &b)
	}
	gw := gateways[0]
	runCtx.mustClearSetOwnerReferencesAndStatusAndWriteObj(&gw.TypeMeta, gw)
	for _, ep := range eps.Items {
		runCtx.mustClearSetOwnerReferencesAndStatusAndWriteObj(&ep.TypeMeta, &ep)
	}

	filterConfigSecret, err := runCtx.fakeClientSet.CoreV1().
		Secrets(envoyGatewayNamespace).Get(ctx,
		controller.FilterConfigSecretPerGatewayName(gw.Name, gw.Namespace), metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get filter config secret: %w", err)
	}

	rawConfig, ok := filterConfigSecret.StringData[controller.FilterConfigKeyInSecret]
	if !ok {
		return nil, fmt.Errorf("failed to get filter config from secret: %w", err)
	}
	var fc filterapi.Config
	if err = yaml.Unmarshal([]byte(rawConfig), &fc); err != nil {
		return nil, fmt.Errorf("failed to unmarshal filter config: %w", err)
	}
	runCtx.stderrLogger.Info("Running external process", "config", fc)
	runCtx.mustStartExtProc(ctx, &fc)
	return fakeClient, nil
}

// mustStartExtProc starts the external process with the given working directory, port, and filter configuration.
func (runCtx *runCmdContext) mustStartExtProc(
	ctx context.Context,
	filterCfg *filterapi.Config,
) {
	marshaled, err := yaml.Marshal(filterCfg)
	if err != nil {
		panic(fmt.Sprintf("BUG: failed to marshal filter config: %v", err))
	}
	configPath := filepath.Join(runCtx.tmpdir, "extproc-config.yaml")
	_ = os.Remove(configPath)
	err = os.WriteFile(configPath, marshaled, 0o600)
	if err != nil {
		panic(fmt.Sprintf("BUG: failed to write extension proc config: %v", err))
	}
	args := []string{
		"--configPath", configPath,
		"--extProcAddr", fmt.Sprintf("unix://%s", runCtx.udsPath),
	}
	if runCtx.isDebug {
		args = append(args, "--logLevel", "debug")
	} else {
		args = append(args, "--logLevel", "warn")
	}
	go func() {
		if err := mainlib.Main(ctx, args, os.Stderr); err != nil {
			runCtx.stderrLogger.Error("Failed to run external processor", "error", err)
		}
	}()
}

// mustClearSetOwnerReferencesAndStatusAndWriteObj clears the owner references and status of the given object, marshals it
// to YAML, and writes it to the output file.
//
// The resources must not have these fields set to be run by the Envoy Gateway agent.
//
// All operation here are done in a panic if an error occurs since the error should not happen in practice.
func (runCtx *runCmdContext) mustClearSetOwnerReferencesAndStatusAndWriteObj(typedMeta *metav1.TypeMeta, obj client.Object) {
	obj.SetOwnerReferences(nil)
	mustSetGroupVersionKind(typedMeta, obj)
	marshaled, err := yaml.Marshal(obj)
	if err != nil {
		panic(err)
	}
	var raw map[string]interface{}
	err = yaml.Unmarshal(marshaled, &raw)
	if err != nil {
		panic(err)
	}
	delete(raw, "status")
	marshaled, err = yaml.Marshal(raw)
	if err != nil {
		panic(err)
	}
	_, err = runCtx.envoyGatewayResourcesOut.Write(append([]byte("---\n"), marshaled...))
	if err != nil {
		panic(err)
	}
}

func (runCtx *runCmdContext) rewriteSecretWithAnnotatedLocation(s *corev1.Secret) (err error) {
	allData := s.Data
	if allData == nil {
		allData = make(map[string][]byte)
	}
	for k, v := range s.StringData {
		allData[k] = []byte(v)
	}
	for k, v := range allData {
		envSubKeyAnnotation := substitutionEnvAnnotationPrefix + k
		fileSubKeyAnnotation := substitutionFileAnnotationPrefix + k
		if envSubKey, ok := s.Annotations[envSubKeyAnnotation]; ok {
			// If this is an environment variable, substitute it.
			envVal := os.Getenv(envSubKey)
			if envVal == "" {
				runCtx.stderrLogger.Warn("Missing environment variable, skipping substitution",
					"annotation_key", envSubKey, "env_key", k, "env_substitution_key", envSubKey)
				continue
			}
			runCtx.stderrLogger.Info("Substituting environment variable", "key", k, "value", envSubKey)
			v = []byte(envVal)
		} else if fileSubKey, ok := s.Annotations[fileSubKeyAnnotation]; ok {
			fileSubPath := maybeResolveHome(fileSubKey)
			// Check the target file exists.
			v, err = os.ReadFile(fileSubPath)
			if err != nil {
				runCtx.stderrLogger.Error("Failed to read substitution file. Skipping substitution", "path", fileSubPath, "error", err)
				continue
			}
			runCtx.stderrLogger.Info("Substituting file", "key", k, "value", fileSubKey)
		}
		allData[k] = v
	}
	s.Data = allData
	s.StringData = nil
	return nil
}

func maybeResolveHome(p string) string {
	if strings.HasPrefix(p, "~/") {
		home, err := os.UserHomeDir()
		if err != nil {
			return p
		}
		return filepath.Join(home, p[2:])
	}
	return p
}
