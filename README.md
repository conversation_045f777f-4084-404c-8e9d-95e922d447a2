# Envoy AI Gateway
Envoy AI Gateway is an open source project for using [Envoy Gateway](https://github.com/envoyproxy/gateway) to handle request traffic from application clients to Generative AI services.

## Contact

* Slack: Join the [Envoy Slack workspace][] if you're not already a member. Otherwise, use the
  [Envoy AI Gateway channel][] to start collaborating with the community.

## Get Involved

We adhere to the [CNCF Code of conduct][Code of conduct]

The Envoy AI Gateway team and community members meet every Thursday.
Please register for the meeting, add agenda points, and get involved. The
meeting details are available in the [public document][meeting].

To contribute to the project via pull requests, please read the [CONTRIBUTING.md](CONTRIBUTING.md) file
which includes information on how to build and test the project.

## Background

The proposal of using Envoy Gateway as a [Cloud Native LLM Gateway][Cloud Native LLM Gateway] inspired the initiation of this project.


[meeting]: https://docs.google.com/document/d/10e1sfsF-3G3Du5nBHGmLjXw5GVMqqCvFDqp_O65B0_w/edit?tab=t.0
[Envoy Slack workspace]: https://communityinviter.com/apps/envoyproxy/envoy
[Envoy AI Gateway channel]: https://envoyproxy.slack.com/archives/C07Q4N24VAA
[Code of conduct]: https://github.com/cncf/foundation/blob/main/code-of-conduct.md
[Cloud Native LLM Gateway]: https://docs.google.com/document/d/1FQN_hGhTNeoTgV5Jj16ialzaSiAxC0ozxH1D9ngCVew/edit?tab=t.0#heading=h.uuu99yemq4eo
