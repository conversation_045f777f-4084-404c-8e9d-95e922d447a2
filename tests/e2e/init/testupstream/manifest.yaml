# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: apps/v1
kind: Deployment
metadata:
  name: testupstream
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: testupstream
  template:
    metadata:
      labels:
        app: testupstream
    spec:
      containers:
        - name: testupstream
          image: docker.io/envoyproxy/ai-gateway-testupstream:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
          env:
            - name: TESTUPSTREAM_ID
              value: primary
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 1
---
apiVersion: v1
kind: Service
metadata:
  name: testupstream
  namespace: default
spec:
  selector:
    app: testupstream
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: testupstream-canary
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: testupstream-canary
  template:
    metadata:
      labels:
        app: testupstream-canary
    spec:
      containers:
        - name: testupstream-canary
          image: docker.io/envoyproxy/ai-gateway-testupstream:latest
          imagePullPolicy: IfNotPresent
          env:
            - name: TESTUPSTREAM_ID
              value: canary
          ports:
            - containerPort: 8080
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 1
---
apiVersion: v1
kind: Service
metadata:
  name: testupstream-canary
  namespace: default
spec:
  selector:
    app: testupstream-canary
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
