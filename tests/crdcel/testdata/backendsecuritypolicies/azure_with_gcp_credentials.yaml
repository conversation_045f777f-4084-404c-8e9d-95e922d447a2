# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: azure-with-gcp-policy
  namespace: default
spec:
  type: AzureCredentials
  gcpCredentials:
    workLoadIdentityFederationConfig:
      projectID: test-project
      workloadIdentityPoolName: test-pool
      workloadIdentityProvider:
        name: test-provider
        OIDCProvider:
          oidc:
            provider:
              issuer: https://test-issuer.com
            clientID: test-client-id
            clientSecret:
              name: gcp-client-secret
