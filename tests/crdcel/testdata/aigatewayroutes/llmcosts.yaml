# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIRoute
metadata:
  name: llmcosts
  namespace: default
spec:
  schema:
    name: OpenAI
  targetRefs:
    - name: some-gateway
      kind: Gateway
      group: gateway.networking.k8s.io
  rules:
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: llama3-70b
      backendRefs:
        - name: kserve
          weight: 20
        - name: aws-bedrock
          weight: 40
        - name: azure-openai
          weight: 40
  llmRequestCosts:
    - metadataKey: llm_input_token
      type: InputToken
    - metadataKey: llm_output_token
      type: OutputToken
    - metadataKey: llm_total_token
      type: TotalToken
    - metadataKey: some_cel_cost
      type: CEL
      cel: "llm_input_token + llm_output_token + llm_total_token"
