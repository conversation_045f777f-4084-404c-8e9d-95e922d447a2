# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: npm
    directory: site
    schedule:
      interval: weekly

  - package-ecosystem: gomod
    directories:
      - "/"
    schedule:
      interval: weekly
    groups:
      k8s.io:
        patterns:
          - "k8s.io/*"
      golang.org:
        patterns:
          - "golang.org/*"
    ignore:
      - dependency-name: "github.com/envoyproxy/gateway"
