name: "CodeQL"

on:
  push:
    branches:
    - "main"
  pull_request:
    branches:
    - "main"
  schedule:
  - cron: '16 11 * * 5'

permissions:
  contents: read


jobs:
  analyze:
    name: Analyze
    runs-on: 'ubuntu-22.04'
    timeout-minutes: 360
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false

    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
    - shell: bash
      run: sudo apt-get install libbtrfs-dev -y
    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5  # v5.0.1
      with:
        go-version-file: go.mod
        cache: true

    - name: Initialize CodeQL
      uses: github/codeql-action/init@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858  # v3.29.0
      with:
        languages: go

    - name: Autobuild
      uses: github/codeql-action/autobuild@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858  # v3.29.0

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858  # v3.29.0
      with:
        category: "/language:go"
