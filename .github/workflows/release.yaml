name: Release
on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+**'  # Ex. v0.2.0 v0.2.1-rc2

jobs:
  docker_push:
    name: Push Docker Images
    uses: ./.github/workflows/docker_builds_template.yaml
    secrets: inherit

  release:
    needs: [docker_push]
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Set HELM_CHART_VERSION and TAG envs
        run: |
          TAG="${GITHUB_REF#refs/tags/}"
          echo "HELM_CHART_VERSION=${TAG}" >> $GITHUB_ENV
          echo "HELM_CHART_VERSION_WITHOUT_V=$(echo ${TAG#v})" >> $GITHUB_ENV
          echo "TAG=${TAG}" >> $GITHUB_ENV

      # To include the helm chart in the release artifact, we build and push it here instead of the separate job.
      - uses: actions/checkout@v4
      - name: Login into DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}
      - name: Push Helm chart
        run: |
          make helm-push HELM_CHART_VERSION=${HELM_CHART_VERSION}
          make helm-push HELM_CHART_VERSION=${HELM_CHART_VERSION_WITHOUT_V}

      - name: Create a release candidate
        if: ${{ contains(github.ref, '-rc') }}
        run: |
          gh release create $TAG --prerelease --title $TAG --notes "Release candidate" ./out/ai-gateway-crds-helm-${TAG}.tgz ./out/ai-gateway-helm-${TAG}.tgz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create a stable release
        if: ${{ !contains(github.ref, '-rc') }}
        run: |
          gh release create $TAG --draft --title $TAG --notes "To be written by the release manager" ./out/ai-gateway-crds-helm-${TAG}.tgz ./out/ai-gateway-helm-${TAG}.tgz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
