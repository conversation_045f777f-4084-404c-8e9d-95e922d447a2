name: Retest Action on PR Comment

on:
  issue_comment:
    types: [created]

permissions:
  contents: read

jobs:
  retest:
    if: |
      ${{
         github.event.issue.pull_request
         && github.repository == 'envoyproxy/ai-gateway'
         && github.actor != 'repokitteh-read-only[bot]'
         && github.actor != 'dependabot[bot]'
      }}
    name: Retest
    runs-on: ubuntu-22.04
    permissions:
      pull-requests: write
      actions: write
    steps:
      - uses: envoyproxy/toolshed/gh-actions/retest@effeefe9b275dc8056f77c0e7b1010c252167d3e  # actions-v0.0.2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
