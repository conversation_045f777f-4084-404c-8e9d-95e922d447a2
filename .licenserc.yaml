# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

header:
  license:
    spdx-id: Apache-2.0
    copyright-owner: Envoy AI Gateway Authors
    content: |
      Copyright Envoy AI Gateway Authors
      SPDX-License-Identifier: Apache-2.0
      The full text of the Apache license is available in the LICENSE file at
      the root of the repo.
  paths-ignore:
    - site
    - out
    - '.github'
    - '.bin'
    - '**/go.mod'
    - '**/go.sum'
    - '**/*.md'
    - '**/*.json'
    - '**/*.txt'
    - '**/*.hcl'
    - '**/.gitignore'
    - '**/.helmignore'
    - .trivyignore
    - '.codespell.*'
    - .editorconfig
    - .golangci.yml
    - .testcoverage.yml
    - '.yamllint*'
    - LICENSE
    - CODEOWNERS
    - pre-commit.sh
    - tests/extproc/testdata/server.key
    - tests/extproc/testdata/server.crt
