run:
  timeout: 10m

linters:
  enable:
    - bodyclose
    - depguard
    - errorlint
    - copyloopvar
    - importas
    - gci
    - gofumpt
    - gocritic
    - gosec
    - govet
    - misspell
    - revive
    - stylecheck
    - testifylint
    - unconvert

linters-settings:
  depguard:
    rules:
      Main:
        deny:
          - pkg: github.com/gogo/protobuf
            desc: "gogo/protobuf is deprecated, use golang/protobuf"
          - pkg: gopkg.in/yaml.v2
            desc: "use sigs.k8s.io/yaml instead"
          - pkg: gopkg.in/yaml.v3
            desc: "use sigs.k8s.io/yaml instead"
          - pkg: k8s.io/utils/pointer
            desc: "use k8s.io/utils/ptr instead"
  importas:
    # Do not allow unaliased imports of aliased packages.
    no-unaliased: true
    # Do not allow non-required aliases.
    no-extra-aliases: false
    alias:
      - pkg: sigs.k8s.io/gateway-api/apis/v1
        alias: gwapiv1
      - pkg: sigs.k8s.io/gateway-api/apis/v1alpha2
        alias: gwapiv1a2
      - pkg: sigs.k8s.io/gateway-api/apis/v1alpha3
        alias: gwapiv1a3
      - pkg: sigs.k8s.io/gateway-api/apis/v1beta1
        alias: gwapiv1b1
      - pkg: github.com/envoyproxy/gateway/api/v1alpha1
        alias: egv1a1
      - pkg: github.com/envoyproxy/ai-gateway/api/v1alpha1
        alias: aigv1a1
      - pkg: sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2
        alias: gwaiev1a2
      - pkg: k8s.io/apimachinery/pkg/apis/meta/v1
        alias: metav1
      - pkg: k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1
        alias: apiextensionsv1
      - pkg: sigs.k8s.io/mcs-api/pkg/apis/v1alpha1
        alias: mcsapiv1a1
      - pkg: k8s.io/api/(\w+)/(v[\w\d]+)
        alias: $1$2
      - pkg: k8s.io/apimachinery/pkg/api/errors
        alias: apierrors
      - pkg: github.com/envoyproxy/ai-gateway/internal/testing
        alias: internaltesting
      - pkg: github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3
        alias: clusterv3
      - pkg: github.com/envoyproxy/go-control-plane/envoy/config/route/v3
        alias: routev3
      - pkg: github.com/envoyproxy/gateway/proto/extension
        alias: egextension
      - pkg: github.com/envoyproxy/go-control-plane/envoy/extensions/filters/network/http_connection_manager/v3
        alias: httpconnectionmanagerv3
  gci:
    sections:
      # Captures all standard packages if they do not match another section.
      - standard
      # Contains all imports that could not be matched to another section type.
      - default
      # Groups all imports with the specified Prefix.
      - prefix(github.com/envoyproxy/ai-gateway)
  govet:
    enable-all: true
    disable:
      - fieldalignment
  testifylint:
    disable:
      - float-compare
      - go-require
    enable:
      - bool-compare
      - compares
      - empty
      - error-is-as
      - error-nil
      - expected-actual
      - len
      - require-error
      - suite-dont-use-pkg
      - suite-extra-assert-call

issues:
  exclude-rules:
    - path: zz_generated
      linters:
        - goimports
    - linters:
        - staticcheck
      text: "SA1019:"
    - path: test/e2e
      linters:
        - bodyclose
  # Show the complete output
  max-issues-per-linter: 0
  max-same-issues: 0
